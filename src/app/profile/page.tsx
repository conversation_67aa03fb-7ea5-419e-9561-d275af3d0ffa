'use client';

import { PromptCard } from '@/components';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { USER_INFO } from '@/data';
import { ScreenPath } from '@/enums';
import { usePrompts } from '@/hooks';
import { Prompt } from '@/types/prompt';
import { cn, formatDate, MONTH_YEAR_FORMAT } from '@/utils';
import {
  ArrowLeft,
  Edit,
  Heart,
  Settings,
  ShoppingBag,
  Upload,
  User
} from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useCallback, useMemo } from 'react';

type TabValue = 'created' | 'liked' | 'purchased';

// Types
interface TabConfig {
  value: TabValue;
  icon: React.ComponentType<{ className?: string }>;
  title: string;
  description: string;
  actionText?: string;
  action?(): void;
}

interface StatItemProps {
  value: number;
  label: string;
  colorClass: string;
}

// Components
const StatItem = ({ value, label, colorClass }: StatItemProps) => (
  <div className='text-center'>
    <div className={cn('text-2xl font-bold', colorClass)}>{value}</div>
    <div className='text-sm text-muted-foreground'>{label}</div>
  </div>
);

const EmptyState = ({
  icon: Icon,
  title,
  description,
  actionText,
  action
}: Omit<TabConfig, 'value' | 'label'>) => (
  <Card className='glass border-white/10 p-5'>
    <CardHeader className='text-center'>
      <Icon className='w-12 h-12 mx-auto mb-4 text-muted-foreground' />
      <CardTitle>{title}</CardTitle>
      <CardDescription>{description}</CardDescription>
    </CardHeader>
    {actionText && (
      <CardContent className='text-center'>
        <Button
          className='gradient-purple-cyan text-white border-0 shadow-glow'
          onClick={action}
        >
          {actionText}
        </Button>
      </CardContent>
    )}
  </Card>
);

const PromptGrid = ({
  prompts,
  onUpvote,
  onDownvote
}: {
  prompts: Prompt[];
  onUpvote: (id: string) => Promise<void>;
  onDownvote: (id: string) => Promise<void>;
}) => (
  <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
    {prompts.map((prompt) => (
      <PromptCard
        key={prompt.id}
        {...prompt}
        onUpvote={onUpvote}
        onDownvote={onDownvote}
      />
    ))}
  </div>
);

export default function ProfilePage() {
  const router = useRouter();

  const {
    myPrompts,
    likedPrompts,
    purchasedPrompts,
    handleUpvote,
    handleDownvote
  } = usePrompts();

  // Tab configuration
  const tabConfigs: TabConfig[] = useMemo(
    () => [
      {
        value: 'created',
        icon: Upload,
        title: 'No prompts created yet',
        description:
          'Start creating and sharing your AI prompts with the community',
        actionText: 'Create Your First Prompt',
        action: () => router.push(ScreenPath.Upload)
      },
      {
        value: 'liked',
        icon: Heart,
        title: 'No liked prompts yet',
        description:
          'Explore the marketplace and like prompts you find interesting'
      },
      {
        value: 'purchased',
        icon: ShoppingBag,
        title: 'No purchased prompts yet',
        description: 'Browse premium prompts and purchase ones that inspire you'
      }
    ],
    [router]
  );

  // Memoized prompt data mapping
  const promptData = useMemo(
    () => ({
      created: myPrompts,
      liked: likedPrompts,
      purchased: purchasedPrompts
    }),
    [myPrompts, likedPrompts, purchasedPrompts]
  );

  const onBack = useCallback(() => {
    router.push(ScreenPath.Home);
  }, [router]);

  // Memoized stats data
  const statsData = useMemo(
    () => [
      {
        value: USER_INFO.stats.totalPrompts,
        label: 'Prompts',
        colorClass: 'text-purple-600'
      },
      {
        value: USER_INFO.stats.totalSales,
        label: 'Sales',
        colorClass: 'text-cyan-700'
      },
      {
        value: USER_INFO.stats.totalEarnings,
        label: 'Earnings',
        colorClass: 'text-green-600'
      },
      {
        value: USER_INFO.stats.followers,
        label: 'Followers',
        colorClass: 'text-orange-600'
      }
    ],
    []
  );

  return (
    <div className='min-h-svh gradient-dark py-10'>
      <div className='max-w-7xl mx-auto px-6'>
        {/* Back Button */}
        <Button
          variant='ghost'
          className='mb-6 hover:shadow-glow transition-all'
          onClick={onBack}
          aria-label='Back to Home'
        >
          <ArrowLeft className='w-4 h-4' />
          Back to Home
        </Button>

        {/* Profile Header */}
        <div className='glass rounded-xl p-8 mb-8'>
          <div className='flex flex-col md:flex-row items-start md:items-center space-y-4 md:space-y-0 md:space-x-6'>
            <Avatar className='w-24 h-24 ring-4 ring-purple-400/30'>
              <AvatarImage
                src={`https://picsum.photos/id/${USER_INFO.avatar}/300/300.jpg`}
                alt={`${USER_INFO.displayName}'s avatar`}
              />
              <AvatarFallback>
                <User className='w-12 h-12' />
              </AvatarFallback>
            </Avatar>
            <div className='flex-1'>
              <div className='flex flex-col sm:flex-row sm:items-center sm:justify-between'>
                <div>
                  <h1 className='text-3xl font-bold'>
                    {USER_INFO.displayName}
                  </h1>
                  <p className='text-muted-foreground'>@{USER_INFO.username}</p>
                </div>
              </div>

              <p className='text-muted-foreground'>{USER_INFO.bio}</p>
              <p className='text-sm text-muted-foreground'>
                Joined {formatDate(USER_INFO.joinDate, MONTH_YEAR_FORMAT)}
              </p>
            </div>

            <div className='flex items-center gap-2 absolute right-5 top-5'>
              <Button className='gradient-purple-cyan text-white border-0 shadow-glow'>
                <Edit className='w-4 h-4' />
                Edit Profile
              </Button>
              <Button
                variant='outline'
                className='glass hover:shadow-glow transition-all'
                aria-label='Settings'
              >
                <Settings className='w-4 h-4' />
              </Button>
            </div>
          </div>

          {/* Stats */}
          <div className='grid grid-cols-2 md:grid-cols-4 gap-4 mt-6 pt-6 border-t border-white/10'>
            {statsData.map((stat, index) => (
              <StatItem
                key={index}
                value={stat.value}
                label={stat.label}
                colorClass={stat.colorClass}
              />
            ))}
          </div>
        </div>

        {/* Content Tabs */}
        <Tabs defaultValue='created' className='w-full'>
          <div className='flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6'>
            <TabsList className='glass'>
              {tabConfigs.map((config) => {
                const Icon = config.icon;
                const count = promptData[config.value as TabValue]?.length || 0;

                return (
                  <TabsTrigger
                    key={config.value}
                    value={config.value}
                    className='flex items-center gap-2 min-w-[110px]'
                  >
                    <Icon className='w-4 h-4' />
                    <span className='capitalize'>
                      {config.value} ({count})
                    </span>
                  </TabsTrigger>
                );
              })}
            </TabsList>
          </div>

          {tabConfigs.map((config) => {
            const prompts = promptData[config.value as TabValue] || [];
            const Icon = config.icon;

            return (
              <TabsContent key={config.value} value={config.value}>
                {prompts.length > 0 ? (
                  <PromptGrid
                    prompts={prompts}
                    onUpvote={handleUpvote}
                    onDownvote={handleDownvote}
                  />
                ) : (
                  <EmptyState
                    icon={Icon}
                    title={config.title}
                    description={config.description}
                    actionText={config.actionText}
                    action={config.action}
                  />
                )}
              </TabsContent>
            );
          })}
        </Tabs>
      </div>
    </div>
  );
}
