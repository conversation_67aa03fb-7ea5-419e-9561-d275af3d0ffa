'use client';

import { PromptCard } from '@/components';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { USER_INFO } from '@/data';
import { ScreenPath } from '@/enums';
import { usePrompts } from '@/hooks';
import { formatDate, MONTH_YEAR_FORMAT } from '@/utils';
import {
  ArrowLeft,
  Edit,
  Heart,
  Settings,
  ShoppingBag,
  Upload,
  User
} from 'lucide-react';
import { useRouter } from 'next/navigation';

export default function ProfilePage() {
  const router = useRouter();

  const {
    myPrompts,
    likedPrompts,
    purchasedPrompts,
    handleUpvote,
    handleDownvote
  } = usePrompts();

  const onBack = () => {
    router.push(ScreenPath.Home);
  };

  return (
    <div className='min-h-svh gradient-dark py-10'>
      <div className='max-w-7xl mx-auto px-6'>
        {/* Back Button */}
        <Button
          variant='ghost'
          className='mb-6 hover:shadow-glow transition-all'
          onClick={onBack}
        >
          <ArrowLeft className='w-4 h-4' />
          Back to Home
        </Button>

        {/* Profile Header */}
        <div className='glass rounded-xl p-8 mb-8'>
          <div className='flex flex-col md:flex-row items-start md:items-center space-y-4 md:space-y-0 md:space-x-6'>
            <Avatar className='w-24 h-24 ring-4 ring-purple-400/30'>
              <AvatarImage
                src={`https://picsum.photos/id/${USER_INFO.avatar}/300/300.jpg`}
              />
              <AvatarFallback>
                <User className='w-12 h-12' />
              </AvatarFallback>
            </Avatar>
            <div className='flex-1'>
              <div className='flex flex-col sm:flex-row sm:items-center sm:justify-between'>
                <div>
                  <h1 className='text-3xl font-bold'>
                    {USER_INFO.displayName}
                  </h1>
                  <p className='text-muted-foreground'>@{USER_INFO.username}</p>
                </div>
              </div>

              <p className='text-muted-foreground'>{USER_INFO.bio}</p>
              <p className='text-sm text-muted-foreground'>
                Joined {formatDate(USER_INFO.joinDate, MONTH_YEAR_FORMAT)}
              </p>
            </div>

            <div className='flex items-center gap-2 absolute right-5 top-5'>
              <Button className='gradient-purple-cyan text-white border-0 shadow-glow'>
                <Edit className='w-4 h-4' />
                Edit Profile
              </Button>
              <Button
                variant='outline'
                className='glass hover:shadow-glow transition-all'
              >
                <Settings className='w-4 h-4' />
              </Button>
            </div>
          </div>

          {/* Stats */}
          <div className='grid grid-cols-2 md:grid-cols-4 gap-4 mt-6 pt-6 border-t border-white/10'>
            <div className='text-center'>
              <div className='text-2xl font-bold text-purple-600'>
                {USER_INFO.stats.totalPrompts}
              </div>
              <div className='text-sm text-muted-foreground'>Prompts</div>
            </div>
            <div className='text-center'>
              <div className='text-2xl font-bold text-cyan-700'>
                {USER_INFO.stats.totalSales}
              </div>
              <div className='text-sm text-muted-foreground'>Sales</div>
            </div>
            <div className='text-center'>
              <div className='text-2xl font-bold text-green-600'>
                ${USER_INFO.stats.totalEarnings}
              </div>
              <div className='text-sm text-muted-foreground'>Earnings</div>
            </div>
            <div className='text-center'>
              <div className='text-2xl font-bold text-orange-600'>
                {USER_INFO.stats.followers}
              </div>
              <div className='text-sm text-muted-foreground'>Followers</div>
            </div>
          </div>
        </div>

        {/* Content Tabs */}
        <Tabs defaultValue='created' className='w-full'>
          <div className='flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6'>
            <TabsList className='glass'>
              <TabsTrigger value='created' className='flex items-center'>
                <Upload className='w-4 h-4' />
                Created ({myPrompts.length})
              </TabsTrigger>
              <TabsTrigger value='liked' className='flex items-center'>
                <Heart className='w-4 h-4' />
                Liked ({likedPrompts.length})
              </TabsTrigger>
              <TabsTrigger value='purchased' className='flex items-center'>
                <ShoppingBag className='w-4 h-4' />
                Purchased ({purchasedPrompts.length})
              </TabsTrigger>
            </TabsList>
          </div>

          <TabsContent value='created'>
            {myPrompts.length < 0 ? (
              <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
                {myPrompts.map((prompt) => (
                  <PromptCard
                    key={prompt.id}
                    {...prompt}
                    onUpvote={handleUpvote}
                    onDownvote={handleDownvote}
                  />
                ))}
              </div>
            ) : (
              <Card className='glass border-white/10 p-5'>
                <CardHeader className='text-center'>
                  <Upload className='w-12 h-12 mx-auto mb-4 text-muted-foreground' />
                  <CardTitle>No prompts created yet</CardTitle>
                  <CardDescription>
                    Start creating and sharing your AI prompts with the
                    community
                  </CardDescription>
                </CardHeader>
                <CardContent className='text-center'>
                  <Button className='gradient-purple-cyan text-white border-0 shadow-glow'>
                    Create Your First Prompt
                  </Button>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value='liked'>
            {likedPrompts.length > 0 ? (
              <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
                {likedPrompts.map((prompt) => (
                  <PromptCard
                    key={prompt.id}
                    {...prompt}
                    onUpvote={handleUpvote}
                    onDownvote={handleDownvote}
                  />
                ))}
              </div>
            ) : (
              <Card className='glass border-white/10 p-5'>
                <CardHeader className='text-center'>
                  <Heart className='w-12 h-12 mx-auto mb-4 text-muted-foreground' />
                  <CardTitle>No liked prompts yet</CardTitle>
                  <CardDescription>
                    Explore the marketplace and like prompts you find
                    interesting
                  </CardDescription>
                </CardHeader>
              </Card>
            )}
          </TabsContent>

          <TabsContent value='purchased'>
            {purchasedPrompts.length > 0 ? (
              <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
                {purchasedPrompts.map((prompt) => (
                  <PromptCard
                    key={prompt.id}
                    {...prompt}
                    onUpvote={handleUpvote}
                    onDownvote={handleDownvote}
                  />
                ))}
              </div>
            ) : (
              <Card className='glass border-white/10 p-5'>
                <CardHeader className='text-center'>
                  <ShoppingBag className='w-12 h-12 mx-auto mb-4 text-muted-foreground' />
                  <CardTitle>No purchased prompts yet</CardTitle>
                  <CardDescription>
                    Browse premium prompts and purchase ones that inspire you
                  </CardDescription>
                </CardHeader>
              </Card>
            )}
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
