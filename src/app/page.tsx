'use client';

import { PromptCard, SearchBar } from '@/components';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { AIModel, AIModelLables } from '@/enums';
import { usePrompts } from '@/hooks';
import { cn } from '@/utils';
import { Clock, Filter, Sparkles, TrendingUp } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useCallback } from 'react';

const AI_MODELS = Object.entries(AIModelLables).map(([value, label]) => ({
  label,
  value: value as AIModel
}));

export default function HomePage() {
  const router = useRouter();

  const {
    filteredTrendingPrompts,
    filteredNewestPrompts,
    filteredAllPrompts,
    searchQuery,
    selectedModel,
    isSearching,
    setSearchQuery,
    handleUpvote,
    handleDownvote,
    handleModelFilter,
    handleSearch,
    clearSearch
  } = usePrompts();

  // Navigation handler
  const handlePromptClick = useCallback(
    (id: string) => {
      router.push(`/prompt/${id}`);
    },
    [router]
  );

  return (
    <div className='min-h-svh gradient-dark pb-10'>
      <div className='max-w-7xl mx-auto px-6'>
        {/* Hero Section */}
        <div className='text-center mb-12'>
          <h1 className='text-4xl md:text-6xl font-bold mb-6 pt-16'>
            Discover the Best <span className='text-gradient'>AI Prompts</span>
          </h1>
          <p className='text-xl text-muted-foreground mb-8 max-w-2xl mx-auto'>
            Find, vote, and sell high-quality prompts for ChatGPT, Midjourney,
            and Sora. Join the ultimate AI prompt marketplace.
          </p>

          {/* Search Bar */}
          <SearchBar
            searchQuery={searchQuery}
            onSearchQueryChange={setSearchQuery}
            onSearch={handleSearch}
            onClear={clearSearch}
            isSearching={isSearching}
            placeholder='Search for prompts, creators, or AI models...'
          />
        </div>

        {/* Quick Filters */}
        <div
          className='flex lg:flex-wrap gap-3 lg:justify-center mb-8 lg:overflow-visible overflow-x-auto snap-x snap-mandatory'
          style={{
            WebkitOverflowScrolling: 'touch'
          }}
        >
          <Button
            variant={selectedModel === 'All' ? 'default' : 'outline'}
            onClick={() => handleModelFilter('All')}
            className={cn(
              'snap-start min-w-[110px]',
              selectedModel === 'All' && 'font-bold'
            )}
          >
            <Filter className='w-4 h-4' />
            All Models
          </Button>
          {AI_MODELS.map((model) => (
            <Button
              key={model.value}
              variant={selectedModel === model.value ? 'default' : 'outline'}
              onClick={() => handleModelFilter(model.value)}
              className={cn(
                'snap-start min-w-[110px]',
                selectedModel === model.value && 'font-bold'
              )}
            >
              {model.label}
            </Button>
          ))}
        </div>

        {/* Prompts Sections */}
        <Tabs defaultValue='all' className='w-full'>
          <div className='flex justify-center mb-8'>
            <TabsList className='glass'>
              <TabsTrigger value='all' className='flex items-center w-28'>
                <Sparkles className='w-4 h-4' />
                All
              </TabsTrigger>
              <TabsTrigger value='trending' className='flex items-center w-28'>
                <TrendingUp className='w-4 h-4' />
                Trending
              </TabsTrigger>
              <TabsTrigger value='newest' className='flex items-center w-28'>
                <Clock className='w-4 h-4' />
                Newest
              </TabsTrigger>
            </TabsList>
          </div>

          <TabsContent value='all'>
            <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
              {filteredAllPrompts.map((prompt) => (
                <PromptCard
                  key={prompt.id}
                  {...prompt}
                  onUpvote={handleUpvote}
                  onDownvote={handleDownvote}
                  onClick={handlePromptClick}
                />
              ))}
            </div>
            {filteredAllPrompts.length === 0 && (
              <div className='text-center py-12'>
                <p className='text-muted-foreground'>
                  No prompts found matching your criteria.
                </p>
              </div>
            )}
          </TabsContent>

          <TabsContent value='trending'>
            <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
              {filteredTrendingPrompts.map((prompt) => (
                <PromptCard
                  key={prompt.id}
                  {...prompt}
                  onUpvote={handleUpvote}
                  onDownvote={handleDownvote}
                  onClick={handlePromptClick}
                />
              ))}
            </div>
            {filteredTrendingPrompts.length === 0 && (
              <div className='text-center py-12'>
                <p className='text-muted-foreground'>
                  No trending prompts found matching your criteria.
                </p>
              </div>
            )}
          </TabsContent>

          <TabsContent value='newest'>
            <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
              {filteredNewestPrompts.map((prompt) => (
                <PromptCard
                  key={prompt.id}
                  {...prompt}
                  onUpvote={handleUpvote}
                  onDownvote={handleDownvote}
                  onClick={handlePromptClick}
                />
              ))}
            </div>
            {filteredNewestPrompts.length === 0 && (
              <div className='text-center py-12'>
                <p className='text-muted-foreground'>
                  No newest prompts found matching your criteria.
                </p>
              </div>
            )}
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
