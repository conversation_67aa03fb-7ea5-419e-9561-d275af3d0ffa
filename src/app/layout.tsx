import { App<PERSON><PERSON><PERSON>, <PERSON><PERSON> } from '@/components';
import { metadata, THEME_SCRIPT } from '@/libs';
import type { Viewport } from 'next';
import './globals.css';

// Viewport configuration for responsive design and PWA
export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 5,
  userScalable: true,
  themeColor: [
    { media: '(prefers-color-scheme: light)', color: '#ffffff' },
    { media: '(prefers-color-scheme: dark)', color: '#0a0a0a' }
  ],
  colorScheme: 'light dark'
};

// Export metadata configuration
export { metadata };

interface RootLayoutProps {
  readonly children: React.ReactNode;
}

/**
 * Root layout component with performance and security optimizations
 *
 * Features:
 * - Optimized font loading with fallbacks
 * - Theme system without FOUC
 * - Security headers and CSP
 * - SEO and accessibility optimizations
 * - Progressive Web App support
 */
export default function RootLayout({ children }: RootLayoutProps) {
  return (
    <html lang='en' suppressHydrationWarning className='scroll-smooth'>
      <head>
        {/* Theme initialization script - must be inline and early */}
        <script
          dangerouslySetInnerHTML={{ __html: THEME_SCRIPT }}
          suppressHydrationWarning
        />

        {/* Prevent zoom on iOS inputs */}
        <meta
          name='viewport'
          content='width=device-width, initial-scale=1, maximum-scale=1, user-scalable=0'
        />

        {/* Security headers */}
        <meta name='referrer' content='strict-origin-when-cross-origin' />
      </head>

      <body
        className={`antialiased min-h-screen bg-background text-foreground`}
        suppressHydrationWarning
      >
        <AppProvider>
          <div className='min-h-screen bg-background'>
            <Header />
            <main className='pt-16'>{children}</main>
          </div>
        </AppProvider>
      </body>
    </html>
  );
}
