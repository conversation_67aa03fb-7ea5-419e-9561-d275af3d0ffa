import { useEffect, useState } from 'react';

/**
 * Custom hook that debounces a value
 *
 * @param value - The value to debounce
 * @param delay - The delay in milliseconds
 * @returns The debounced value
 */
export function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
}

/**
 * Custom hook for debounced search functionality
 *
 * @param initialValue - Initial search query
 * @param delay - Debounce delay in milliseconds (default: 300ms)
 * @returns Object with search state and handlers
 */
export function useDebouncedSearch(initialValue = '', delay = 500) {
  const [searchQuery, setSearchQuery] = useState(initialValue);
  const [isSearching, setIsSearching] = useState(false);
  const debouncedSearchQuery = useDebounce(searchQuery, delay);

  // Track searching state
  useEffect(() => {
    if (searchQuery !== debouncedSearchQuery) {
      setIsSearching(true);
    } else {
      setIsSearching(false);
    }
  }, [searchQuery, debouncedSearchQuery]);

  const clearSearch = () => {
    setSearchQuery('');
    setIsSearching(false);
  };

  const handleSearchChange = (query: string) => {
    setSearchQuery(query);
  };

  return {
    searchQuery,
    debouncedSearchQuery,
    isSearching,
    setSearchQuery: handleSearchChange,
    clearSearch
  };
}
