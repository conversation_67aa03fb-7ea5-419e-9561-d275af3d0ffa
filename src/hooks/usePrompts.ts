import {
  LIKED_PROMPTS_DATA,
  MY_PROMPTS_DATA,
  NEWEST_PROMPTS_DATA,
  PURCHASED_PROMPTS_DATA,
  TRENDING_PROMPTS_DATA
} from '@/data';
import { AIModel } from '@/enums';
import {
  ModelFilterHandler,
  Prompt,
  PromptFilter,
  VoteHandler
} from '@/types/prompt';
import { useCallback, useMemo, useState } from 'react';
import { useDebouncedSearch } from './useDebounce';

interface UsePromptsReturn {
  // Data
  trendingPrompts: Prompt[];
  newestPrompts: Prompt[];
  myPrompts: Prompt[];
  likedPrompts: Prompt[];
  purchasedPrompts: Prompt[];
  filteredTrendingPrompts: Prompt[];
  filteredNewestPrompts: Prompt[];
  filteredAllPrompts: Prompt[];

  // State
  searchQuery: string;
  debouncedSearchQuery: string;
  selectedModel: AIModel | 'All';
  votes: Record<string, number>;
  isSearching: boolean;

  // Actions
  setSearchQuery: (query: string) => void;
  handleUpvote: VoteHandler;
  handleDownvote: VoteHandler;
  handleModelFilter: ModelFilterHandler;
  handleSearch: () => void;
  clearSearch: () => void;
}

// Constants

export function usePrompts(): UsePromptsReturn {
  const {
    searchQuery,
    debouncedSearchQuery,
    isSearching,
    setSearchQuery,
    clearSearch
  } = useDebouncedSearch('');

  // Other state
  const [selectedModel, setSelectedModel] = useState<AIModel | 'All'>('All');
  const [votes, setVotes] = useState<Record<string, number>>({});

  // Memoized data with votes
  const trendingPrompts = useMemo(
    () =>
      TRENDING_PROMPTS_DATA.map((prompt) => ({
        ...prompt,
        votes: votes[prompt.id] || 0
      })),
    [votes]
  );
  const newestPrompts = useMemo(
    () =>
      NEWEST_PROMPTS_DATA.map((prompt) => ({
        ...prompt,
        votes: votes[prompt.id] || 0
      })),
    [votes]
  );

  const myPrompts = useMemo(
    () =>
      MY_PROMPTS_DATA.map((prompt) => ({
        ...prompt,
        votes: votes[prompt.id] || 0
      })),
    [votes]
  );

  const likedPrompts = useMemo(
    () =>
      LIKED_PROMPTS_DATA.map((prompt) => ({
        ...prompt,
        votes: votes[prompt.id] || 0
      })),
    [votes]
  );

  const purchasedPrompts = useMemo(
    () =>
      PURCHASED_PROMPTS_DATA.map((prompt) => ({
        ...prompt,
        votes: votes[prompt.id] || 0
      })),
    [votes]
  );

  // Filter function using debounced search query
  const filterPrompts: PromptFilter = useCallback(
    (prompts: Prompt[]) => {
      return prompts.filter((prompt) => {
        const matchesSearch =
          !debouncedSearchQuery ||
          prompt.title
            .toLowerCase()
            .includes(debouncedSearchQuery.toLowerCase()) ||
          prompt.author
            .toLowerCase()
            .includes(debouncedSearchQuery.toLowerCase()) ||
          prompt.model
            .toLowerCase()
            .includes(debouncedSearchQuery.toLowerCase());

        const matchesModel =
          selectedModel === 'All' || prompt.model === selectedModel;

        return matchesSearch && matchesModel;
      });
    },
    [debouncedSearchQuery, selectedModel]
  );

  // Filtered data
  const filteredTrendingPrompts = useMemo(
    () => filterPrompts(trendingPrompts),
    [filterPrompts, trendingPrompts]
  );

  const filteredNewestPrompts = useMemo(
    () => filterPrompts(newestPrompts),
    [filterPrompts, newestPrompts]
  );

  const filteredAllPrompts = useMemo(
    () => filterPrompts([...trendingPrompts, ...newestPrompts]),
    [filterPrompts, trendingPrompts, newestPrompts]
  );

  // Event handlers
  const handleUpvote: VoteHandler = useCallback(async (id: string) => {
    setVotes((prev) => ({ ...prev, [id]: (prev[id] || 0) + 1 }));
  }, []);

  const handleDownvote: VoteHandler = useCallback(async (id: string) => {
    setVotes((prev) => ({ ...prev, [id]: Math.max((prev[id] || 0) - 1, 0) }));
  }, []);

  const handleModelFilter: ModelFilterHandler = useCallback(
    (model: AIModel | 'All') => {
      setSelectedModel(model);
    },
    []
  );

  const handleSearch = useCallback(() => {
    // In a real app, this would trigger an API call
    console.log('Searching for:', debouncedSearchQuery);
  }, [debouncedSearchQuery]);

  return {
    // Data
    trendingPrompts,
    newestPrompts,
    myPrompts,
    likedPrompts,
    purchasedPrompts,
    filteredTrendingPrompts,
    filteredNewestPrompts,
    filteredAllPrompts,

    // State
    searchQuery,
    debouncedSearchQuery,
    selectedModel,
    votes,
    isSearching,

    // Actions
    setSearchQuery,
    handleUpvote,
    handleDownvote,
    handleModelFilter,
    handleSearch,
    clearSearch
  };
}
