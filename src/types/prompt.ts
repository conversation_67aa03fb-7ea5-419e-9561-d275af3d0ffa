// Prompt-related types and interfaces

import { AIModel } from '@/enums';

export interface Prompt {
  id: string;
  title: string;
  author: string;
  model: AIModel;
  votes?: number;
  thumbnail?: string;
  isPremium?: boolean;
  price?: number;
  isUpvoted: boolean;
}

export type TabValue = 'trending' | 'newest';

// Vote-related types
export type VoteType = 'upvote' | 'downvote' | null;

export interface UserVote {
  promptId: string;
  type: VoteType;
  timestamp: number;
}

export interface VoteState {
  userVotes: Record<string, VoteType>; // promptId -> vote type
  voteCounts: Record<string, number>; // promptId -> total votes
  isLoading: Record<string, boolean>; // promptId -> loading state
}

export interface VoteResult {
  success: boolean;
  newVoteCount?: number;
  error?: string;
}

// Event handler types
export type VoteHandler = (id: string) => Promise<void>;
export type PromptClickHandler = (id: string) => void;
export type ModelFilterHandler = (model: AIModel | 'All') => void;

// Filter function type
export type PromptFilter = (prompts: Prompt[]) => Prompt[];
