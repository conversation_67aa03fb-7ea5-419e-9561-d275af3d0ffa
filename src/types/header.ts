import { ScreenPath } from '@/enums';
import { UserInfo } from '@/types';

export interface NavigationItem {
  label: string;
  icon: React.ComponentType<{ className?: string }>;
  path: ScreenPath;
}

export interface DesktopNavigationProps {
  currentScreen: ScreenPath;
  onNavigate: (screen: ScreenPath) => void;
  isDarkMode: boolean;
  onThemeToggle: () => void;
}

export interface MobileMenuProps {
  isOpen: boolean;
  currentScreen: ScreenPath;
  isDarkMode: boolean;
  userInfo: UserInfo;
  onOpenChange: (open: boolean) => void;
  onNavigate: (screen: ScreenPath) => void;
  onThemeToggle: () => void;
}

export interface LogoProps {
  onClick: () => void;
}

export interface ThemeToggleProps {
  isDarkMode: boolean;
  isMobile?: boolean;
  onToggle: () => void;
}

export interface NavigationButtonProps {
  item: NavigationItem;
  isActive: boolean;
  isMobile?: boolean;
  onClick: () => void;
}
