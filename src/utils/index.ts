import { clsx, type ClassValue } from 'clsx';
import dayjs, { Dayjs } from 'dayjs';
import { twMerge } from 'tailwind-merge';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export function generateRandomId() {
  return String(Math.floor(Math.random() * 1000));
}

export const DEFAULT_FORMAT_DATE = 'DD/MMM/YYYY HH:mmA';
export const MONTH_YEAR_FORMAT = 'MMM YYYY';

export function formatDate(
  date: number | string | Dayjs,
  format = DEFAULT_FORMAT_DATE
) {
  return dayjs(date).format(format);
}
