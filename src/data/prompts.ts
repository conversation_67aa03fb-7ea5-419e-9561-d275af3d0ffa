import { AIModel } from '@/enums';
import { Prompt } from '@/types';
import { generateRandomId } from '@/utils';

export const TRENDING_PROMPTS_DATA: Prompt[] = [
  {
    id: '1',
    title: 'Create Stunning Product Photography',
    author: 'sarah_designs',
    model: AIModel.GPT4,
    thumbnail: generateRandomId(),
    isPremium: true,
    price: 4.99,
    isUpvoted: true
  },
  {
    id: '2',
    title: 'Professional Email Templates',
    author: 'biz_writer',
    model: AIModel.GPT4,
    thumbnail: generateRandomId(),
    isUpvoted: true
  },
  {
    id: '3',
    title: 'Cinematic AI Video Prompts',
    author: 'film_creator',
    model: AIModel.GEMINI,
    thumbnail: generateRandomId(),
    isPremium: true,
    price: 7.99,
    isUpvoted: true
  },
  {
    id: '7',
    title: 'AI-Powered Artwork',
    author: 'art_wizard',
    model: AIModel.GEMINI,
    thumbnail: generateRandomId(),
    isUpvoted: true
  },
  {
    id: '8',
    title: 'AI-Powered Artwork',
    author: 'art_wizard',
    model: AIModel.GEMINI_PRO,
    thumbnail: generateRandomId(),
    isUpvoted: true
  }
];

export const NEWEST_PROMPTS_DATA: Prompt[] = [
  {
    id: '4',
    title: 'Minimalist Logo Design',
    author: 'designpro',
    model: AIModel.GPT4_TURBO,
    isPremium: true,
    price: 3.99,
    thumbnail: generateRandomId(),
    isUpvoted: true
  },
  {
    id: '5',
    title: 'Social Media Caption Generator',
    author: 'content_king',
    model: AIModel.GROK,
    thumbnail: generateRandomId(),
    isPremium: true,
    price: 2.99,
    isUpvoted: true
  },
  {
    id: '6',
    title: 'Abstract Art Animations',
    author: 'art_wizard',
    model: AIModel.KIRO,
    thumbnail: generateRandomId(),
    isUpvoted: true
  }
];

export const MY_PROMPTS_DATA: Prompt[] = [
  {
    id: '9',
    title: 'Minimalist Logo Design',
    author: 'You',
    model: AIModel.GPT4_TURBO,
    isPremium: true,
    price: 3.99,
    thumbnail: generateRandomId(),
    isUpvoted: true
  },
  {
    id: '10',
    title: 'Social Media Caption Generator',
    author: 'You',
    model: AIModel.GROK,
    thumbnail: generateRandomId(),
    isPremium: true,
    price: 2.99,
    isUpvoted: true
  },
  {
    id: '11',
    title: 'Abstract Art Animations',
    author: 'You',
    model: AIModel.KIRO,
    thumbnail: generateRandomId(),
    isUpvoted: true
  }
];

export const LIKED_PROMPTS_DATA: Prompt[] = [
  {
    id: '12',
    title: 'Professional Headshots',
    author: 'photo_master',
    model: AIModel.MIDJOURNEY,
    votes: 67,
    thumbnail: generateRandomId(),
    isPremium: true,
    price: 5.99,
    isUpvoted: true
  },
  {
    id: '13',
    title: 'Email Marketing Copy',
    author: 'copywriter_pro',
    model: AIModel.GPT4,
    votes: 89,
    isUpvoted: true
  }
];

export const PURCHASED_PROMPTS_DATA: Prompt[] = [
  {
    id: '14',
    title: 'Cinematic Video Prompts',
    author: 'film_creator',
    model: AIModel.SORA,
    votes: 156,
    thumbnail: generateRandomId(),
    isPremium: true,
    price: 7.99,
    isUpvoted: true
  },
  {
    id: '15',
    title: 'Advanced Code Generation',
    author: 'dev_ninja',
    model: AIModel.GPT4,
    votes: 234,
    isPremium: true,
    price: 4.99,
    isUpvoted: true
  }
];
