import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Loader, Search, X } from 'lucide-react';

interface SearchBarProps {
  searchQuery: string;
  isSearching?: boolean;
  placeholder?: string;
  className?: string;
  onSearchQueryChange: (query: string) => void;
  onSearch: () => void;
  onClear?: () => void;
}

export function SearchBar({
  searchQuery,
  isSearching = false,
  placeholder = 'Search for prompts, creators, or AI models...',
  className = '',
  onSearchQueryChange,
  onSearch,
  onClear
}: SearchBarProps) {
  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      onSearch();
    }
  };

  const handleClear = () => {
    onClear?.();
  };

  return (
    <div className={`max-w-2xl mx-auto relative ${className}`}>
      <div className='glass rounded-full p-2 flex items-center'>
        {isSearching ? (
          <Loader className='w-5 h-5 text-muted-foreground ml-4 animate-spin' />
        ) : (
          <Search className='w-5 h-5 text-muted-foreground ml-4' />
        )}
        <Input
          placeholder={placeholder}
          value={searchQuery}
          onChange={(e) => onSearchQueryChange(e.target.value)}
          onKeyDown={handleKeyDown}
          className='border-0 bg-transparent flex-1 px-4 focus-visible:ring-0'
        />
        {searchQuery && (
          <Button
            variant='ghost'
            size='sm'
            onClick={handleClear}
            className='mr-2 h-8 w-8 p-0 hover:bg-gray-100 rounded-full'
          >
            <X className='w-4 h-4' />
          </Button>
        )}
      </div>
    </div>
  );
}
