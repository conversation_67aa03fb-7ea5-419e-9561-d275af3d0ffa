import { ImageWithFallback } from '@/components';
import { VoteHandler } from '@/types';
import { cn } from '@/utils';
import { ChevronDown, ChevronUp, Heart, Zap } from 'lucide-react';
import { Badge } from './ui/badge';
import { Button } from './ui/button';

interface PromptCardProps {
  id: string;
  title: string;
  author: string;
  model: string;
  votes?: number;
  thumbnail?: string;
  isPremium?: boolean;
  price?: number;
  isUpvoted?: boolean;
  onUpvote: VoteHandler;
  onDownvote: VoteHandler;
  onClick?: (id: string) => void;
}

export function PromptCard(props: PromptCardProps) {
  const {
    id,
    title,
    author,
    model,
    votes,
    thumbnail,
    isPremium = false,
    price,
    isUpvoted = false,
    onUpvote,
    onDownvote,
    onClick
  } = props;

  return (
    <div
      className='card-light rounded-xl p-6 hover:card-light cursor-pointer group theme-transition'
      onClick={() => onClick?.(id)}
    >
      <div className='flex gap-4 h-full'>
        <div className='flex flex-col space-y-2'>
          <div className='flex flex-col items-center space-y-1'>
            <Button
              variant='ghost'
              size='sm'
              className={cn(
                'p-1 h-8 w-8 text-muted-foreground hover:text-purple-400 transition-colors rounded-full',
                isUpvoted && 'text-purple-400'
              )}
              onClick={(e) => {
                e.stopPropagation();
                onUpvote?.(id);
              }}
            >
              <ChevronUp className='w-4 h-4' />
            </Button>
            <div
              className={cn(
                'text-muted-foreground text-sm font-bold',
                votes && 'text-purple-400'
              )}
            >
              {votes}
            </div>
            <Button
              variant='ghost'
              size='sm'
              className='p-1 h-8 w-8 text-muted-foreground hover:text-red-400 transition-colors rounded-full'
              onClick={(e) => {
                e.stopPropagation();
                onDownvote?.(id);
              }}
            >
              <ChevronDown className='w-4 h-4' />
            </Button>
          </div>
          <Button
            variant='ghost'
            size='sm'
            className='p-1 h-8 w-8 text-muted-foreground hover:text-red-700 transition-colors rounded-full'
          >
            <Heart className='w-4 h-4' />
          </Button>
        </div>

        <div className='flex flex-col flex-1 gap-4 h-full'>
          <div className='w-ful aspect-video rounded-lg overflow-hidden shadow-soft relative'>
            <ImageWithFallback
              src={`https://picsum.photos/id/${thumbnail}/200/300.jpg`}
              alt={title}
              fill
              sizes='(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw'
              className='object-cover'
              priority={false}
            />
          </div>
          <div className='flex items-start justify-between grow'>
            <h3 className='font-semibold text-lg group-hover:text-primary transition-colors'>
              {title}
            </h3>
          </div>
          <div className='flex flex-col gap-4 '>
            {isPremium ? (
              <div className='flex items-center space-x-2'>
                <Badge className='gradient-purple-cyan text-white border-0 shadow-soft py-1 px-2'>
                  <Zap className='w-3 h-3' />
                  Premium
                </Badge>
                {price && (
                  <span className='text-sm font-bold text-green-600'>
                    ${price}
                  </span>
                )}
              </div>
            ) : (
              <div className='flex items-center space-x-2'>
                <Badge variant='secondary' className='glass border-light'>
                  Free
                </Badge>
              </div>
            )}
            <div className='flex items-center justify-between'>
              <div className='flex items-center space-x-3'>
                <span className='text-sm text-muted-foreground'>
                  by {author}
                </span>
                <Badge variant='secondary' className='glass border-light'>
                  {model}
                </Badge>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
